# TaskMaster: Process Control and Optimization

## Project Overview

TaskMaster is a GUI-based task manager application designed to enhance process management and resource utilization on Windows systems. It provides real-time monitoring of system resources, process management capabilities, and historical data analysis to help users optimize their system performance.

## Step-by-Step Workflow

### 1. Initialization

When the application starts (from main.py):
1. A QApplication instance is created
2. The MainWindow class is instantiated
3. During MainWindow initialization:
   - The ProcessManager is created to handle process monitoring
   - The DatabaseManager is initialized to manage data storage
   - The HistoryAnalyzer is set up for historical data analysis
   - A background monitoring thread (ProcessMonitorThread) is started
   - Database update timers are configured
   - The UI is constructed

### 2. Process Monitoring

The core functionality revolves around process monitoring:
1. The ProcessMonitorThread continuously runs in the background
2. It periodically calls ProcessManager.update_all() to refresh process data
3. The ProcessManager:
   - Gets a list of all running processes using psutil
   - Sorts processes by CPU usage and selects the top processes
   - Updates the internal process list
   - Updates system resource information (CPU, memory, disk)
4. The UI is updated every 2 seconds to reflect the latest data

### 3. User Interface Components

The UI consists of several key components:
1. Main Window (MainWindow):
   - Process list table showing PID, name, user, CPU%, and memory%
   - Process information panel displaying details of the selected process
   - Toolbar with refresh, performance, and history buttons
   - Search functionality to filter processes
2. Performance Dialog (PerformanceDialog):
   - Real-time charts showing CPU, memory, and disk usage
   - Updated continuously as new data comes in
3. History Dialog (HistoryDialog):
   - Historical charts of system resource usage
   - Analysis of top processes over time
   - Process-specific trend analysis

### 4. Database Operations

Data persistence is handled through SQLite:
1. The DatabaseManager creates and manages the database structure
2. Process and system data are periodically saved to the database
3. Historical data is retrieved for analysis and visualization
4. Old data is automatically cleaned up to prevent database bloat

### 5. User Interactions

Users can interact with the application in several ways:
1. View process list and sort by different columns
2. Search for specific processes
3. Select a process to view detailed information
4. Terminate processes
5. Change process priorities
6. View real-time performance graphs
7. Analyze historical performance data
8. Start new processes

### 6. Shutdown

When the application is closed:
1. The background monitoring thread is stopped
2. Database connections are closed
3. Resources are released
4. The application exits

## Major Components and Interactions

### Core Components

1. **ProcessManager** (process_monitor.py)
   - Central component for process monitoring
   - Maintains a list of running processes
   - Provides methods to get process information, terminate processes, and set priorities
   - Contains SystemMonitor for overall system resource tracking

2. **ProcessMonitorThread** (process_monitor_thread.py)
   - Background thread that periodically updates process information
   - Prevents UI freezing during data collection
   - Emits signals when updates are complete

3. **DatabaseManager** (database_manager.py)
   - Handles database operations
   - Creates and maintains database tables
   - Provides methods to store and retrieve process and system data

4. **HistoryAnalyzer** (history_analyzer.py)
   - Analyzes historical data from the database
   - Generates statistics and trends
   - Creates visualization charts for historical data

5. **DataStorage** (data_storage.py)
   - Alternative data storage mechanism
   - Handles logging of system snapshots, process snapshots, and events

### GUI Components

1. **MainWindow** (main_window.py)
   - Primary user interface
   - Contains process list and information panels
   - Manages user interactions and navigation

2. **ChartsWidget** (charts_widget.py)
   - Displays real-time performance graphs
   - Updates continuously with new data

3. **PerformanceDialog** (performance_dialog.py)
   - Shows detailed system performance information
   - Contains multiple charts and statistics

4. **HistoryDialog** (history_dialog.py)
   - Displays historical performance data
   - Shows trends and patterns over time

5. **ProcessDetailDialog** (process_detail_dialog.py)
   - Shows detailed information about a specific process
   - Provides additional management options

6. **SystemMonitorWidget** (system_monitor_widget.py)
   - Displays system resource usage
   - Shows CPU, memory, and disk utilization

## Technology Stack

### Libraries and Frameworks

1. **PyQt6**
   - Core GUI framework
   - Provides widgets, layouts, and event handling
   - Enables cross-platform compatibility
   - Components used:
     - QtWidgets: UI elements (buttons, tables, layouts)
     - QtCore: Signals, slots, threading
     - QtGui: Styling, fonts, colors

2. **psutil**
   - Process and system utilities library
   - Retrieves information about running processes
   - Monitors system resources (CPU, memory, disk)
   - Enables process management (termination, priority)

3. **SQLite3**
   - Embedded database engine
   - Stores process and system history
   - Enables data persistence without external dependencies
   - Used through Python's sqlite3 module

4. **pandas**
   - Data analysis library
   - Processes and analyzes historical data
   - Performs statistical calculations
   - Prepares data for visualization

5. **matplotlib**
   - Visualization library
   - Creates charts and graphs
   - Displays performance data visually
   - Integrated with PyQt6 through QtAgg backend

6. **datetime**
   - Handles time-related operations
   - Timestamps data entries
   - Calculates time differences
   - Formats time for display

### Design Patterns

1. **Model-View Pattern**
   - Separation of data (process information) from presentation (UI)
   - Enables multiple views of the same data

2. **Observer Pattern**
   - Signal/slot mechanism for updates
   - UI components observe and react to data changes

3. **Thread-based Concurrency**
   - Background thread for monitoring
   - Prevents UI freezing during data collection

## Data Flow

1. Process data is collected by ProcessManager using psutil
2. Data is passed to the UI for display
3. Data is periodically saved to the database
4. Historical data is retrieved and analyzed when needed
5. Analysis results are displayed in charts and statistics

## Performance Considerations

1. Only top processes are monitored to reduce resource usage
2. UI updates are throttled to 2 seconds to balance responsiveness and performance
3. Database updates occur every 5 minutes to reduce disk I/O
4. Chart rendering is optimized to minimize CPU usage
5. Old data is automatically cleaned up to prevent database bloat

## Conclusion

TaskMaster provides a comprehensive solution for process monitoring and management. Its modular architecture separates concerns between data collection, storage, analysis, and presentation. The use of background threading ensures a responsive UI while continuously monitoring system resources. The combination of real-time monitoring and historical analysis makes it a powerful tool for system optimization and troubleshooting.
