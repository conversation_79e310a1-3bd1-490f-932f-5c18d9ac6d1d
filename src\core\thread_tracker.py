"""
Thread tracking module for monitoring process thread lifecycle.
"""

import psutil
import time
import threading
from collections import defaultdict, deque
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

@dataclass
class ThreadSnapshot:
    """Represents a snapshot of thread information at a specific time."""
    timestamp: float
    thread_count: int
    thread_ids: List[int]
    cpu_time: float
    
@dataclass
class ThreadStats:
    """Comprehensive thread statistics for a process."""
    active: int
    completed: int
    total_created: int
    waiting: int
    running: int
    peak_threads: int
    avg_threads: float
    thread_creation_rate: float  # threads per minute

class ThreadTracker:
    """Tracks thread lifecycle changes for processes over time."""
    
    def __init__(self, history_duration_minutes: int = 30):
        """Initialize the thread tracker.
        
        Args:
            history_duration_minutes: How long to keep thread history
        """
        self.history_duration = timedelta(minutes=history_duration_minutes)
        self.process_histories: Dict[int, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.process_peak_threads: Dict[int, int] = defaultdict(int)
        self.process_total_created: Dict[int, int] = defaultdict(int)
        self.lock = threading.Lock()
        
    def update_process_threads(self, pid: int) -> Optional[ThreadSnapshot]:
        """Update thread information for a specific process.
        
        Args:
            pid: Process ID to update
            
        Returns:
            ThreadSnapshot if successful, None if process not accessible
        """
        try:
            process = psutil.Process(pid)
            current_time = time.time()
            
            # Get current thread information
            thread_count = process.num_threads()
            
            # Get thread IDs if available
            thread_ids = []
            cpu_time = 0.0
            try:
                threads = process.threads()
                thread_ids = [t.id for t in threads]
                cpu_time = sum(t.user_time + t.system_time for t in threads)
            except (psutil.AccessDenied, AttributeError):
                # Fallback if detailed thread info not available
                thread_ids = list(range(thread_count))  # Dummy IDs
                try:
                    cpu_times = process.cpu_times()
                    cpu_time = cpu_times.user + cpu_times.system
                except:
                    cpu_time = 0.0
            
            snapshot = ThreadSnapshot(
                timestamp=current_time,
                thread_count=thread_count,
                thread_ids=thread_ids,
                cpu_time=cpu_time
            )
            
            with self.lock:
                history = self.process_histories[pid]
                
                # Add new snapshot
                history.append(snapshot)
                
                # Update peak threads
                self.process_peak_threads[pid] = max(
                    self.process_peak_threads[pid], 
                    thread_count
                )
                
                # Estimate total threads created by tracking thread ID changes
                if len(history) > 1:
                    prev_snapshot = history[-2]
                    
                    # Count new thread IDs that weren't in previous snapshot
                    new_threads = set(thread_ids) - set(prev_snapshot.thread_ids)
                    self.process_total_created[pid] += len(new_threads)
                else:
                    # First snapshot - assume all current threads were created
                    self.process_total_created[pid] = thread_count
                
                # Clean old history
                self._cleanup_old_history(pid, current_time)
            
            return snapshot
            
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            return None
    
    def get_thread_stats(self, pid: int) -> ThreadStats:
        """Get comprehensive thread statistics for a process.
        
        Args:
            pid: Process ID
            
        Returns:
            ThreadStats object with detailed thread information
        """
        with self.lock:
            history = self.process_histories[pid]
            
            if not history:
                # No history available, try to get current info
                snapshot = self.update_process_threads(pid)
                if not snapshot:
                    return ThreadStats(0, 0, 0, 0, 0, 0, 0.0, 0.0)
                history = self.process_histories[pid]
            
            current_snapshot = history[-1]
            active_threads = current_snapshot.thread_count
            
            # Calculate completed threads
            total_created = self.process_total_created[pid]
            completed_threads = max(0, total_created - active_threads)
            
            # Calculate average threads over time
            if len(history) > 1:
                avg_threads = sum(s.thread_count for s in history) / len(history)
            else:
                avg_threads = active_threads
            
            # Calculate thread creation rate (threads per minute)
            creation_rate = 0.0
            if len(history) > 1:
                time_span = history[-1].timestamp - history[0].timestamp
                if time_span > 0:
                    creation_rate = (total_created / time_span) * 60  # per minute
            
            # Estimate thread states based on CPU activity and thread count changes
            running_threads, waiting_threads = self._estimate_thread_states(pid, history)
            
            peak_threads = self.process_peak_threads[pid]
            
            return ThreadStats(
                active=active_threads,
                completed=completed_threads,
                total_created=total_created,
                waiting=waiting_threads,
                running=running_threads,
                peak_threads=peak_threads,
                avg_threads=avg_threads,
                thread_creation_rate=creation_rate
            )
    
    def _estimate_thread_states(self, pid: int, history: deque) -> tuple:
        """Estimate running vs waiting threads based on CPU activity.
        
        Args:
            pid: Process ID
            history: Thread history for the process
            
        Returns:
            Tuple of (running_threads, waiting_threads)
        """
        if len(history) < 2:
            return (1, max(0, history[-1].thread_count - 1)) if history else (0, 0)
        
        current = history[-1]
        previous = history[-2]
        
        # Calculate CPU time change
        cpu_time_delta = current.cpu_time - previous.cpu_time
        time_delta = current.timestamp - previous.timestamp
        
        if time_delta > 0:
            cpu_usage_rate = cpu_time_delta / time_delta
            
            # Estimate running threads based on CPU activity
            # This is a heuristic: more CPU time change suggests more active threads
            if cpu_usage_rate > 0.1:  # High activity
                running_threads = min(current.thread_count, max(1, int(current.thread_count * 0.7)))
            elif cpu_usage_rate > 0.01:  # Medium activity
                running_threads = min(current.thread_count, max(1, int(current.thread_count * 0.3)))
            else:  # Low activity
                running_threads = min(current.thread_count, 1)
        else:
            running_threads = 1
        
        waiting_threads = max(0, current.thread_count - running_threads)
        
        return (running_threads, waiting_threads)
    
    def _cleanup_old_history(self, pid: int, current_time: float):
        """Remove old history entries beyond the retention period.
        
        Args:
            pid: Process ID
            current_time: Current timestamp
        """
        history = self.process_histories[pid]
        cutoff_time = current_time - self.history_duration.total_seconds()
        
        while history and history[0].timestamp < cutoff_time:
            history.popleft()
    
    def cleanup_dead_processes(self):
        """Remove tracking data for processes that no longer exist."""
        with self.lock:
            dead_pids = []
            for pid in list(self.process_histories.keys()):
                try:
                    psutil.Process(pid)
                except psutil.NoSuchProcess:
                    dead_pids.append(pid)
            
            for pid in dead_pids:
                del self.process_histories[pid]
                if pid in self.process_peak_threads:
                    del self.process_peak_threads[pid]
                if pid in self.process_total_created:
                    del self.process_total_created[pid]
    
    def get_tracked_processes(self) -> List[int]:
        """Get list of currently tracked process IDs."""
        with self.lock:
            return list(self.process_histories.keys())
